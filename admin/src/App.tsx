import React, { useEffect } from "react";
import { ThemeProvider } from "styled-components";
import { Main } from "./components/router/Main";
import getTheme from "@src/config/theme";
import { useRecoilValue } from "recoil";
import { accountThemeAtom } from "./components/authentication/state";

export const App = () => {

	const accountTheme = null
	const theme = getTheme(accountTheme);

	useEffect(() => {
		document.body.style.backgroundColor = theme.colors.apBackgroundColor;
	}, [theme.colors.apBackgroundColor]);

	return (
		<ThemeProvider theme={theme}>
			<Main />
		</ThemeProvider>
	);
};
