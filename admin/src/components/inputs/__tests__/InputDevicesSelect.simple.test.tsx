import React from "react";
import { render, cleanup, act, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { ThemeProvider } from "styled-components";
import { RecoilRoot } from "recoil";
import getTheme from "@src/config/theme";

// Mock all the dependencies at the top level
jest.mock("../../hooks/translations", () => ({
    useTranslation: () => ({
        general: {
            inputDevices: "Input Devices",
            camera: "Camera", 
            microphone: "Microphone",
            screen: "Screen"
        }
    })
}));

jest.mock("../../svg/SVG", () => {
    return function SVG() { return <div data-testid="svg" />; };
});

jest.mock("@src/assets", () => ({
    HelpIcon: "help-icon",
    MicIcon: "mic-icon"
}));

jest.mock("@src/styles/components", () => ({
    PageSection: ({ children }: any) => <div>{children}</div>,
    Flex: ({ children }: any) => <div>{children}</div>,
    InlineIcon: ({ children }: any) => <span>{children}</span>,
    SmallSelectButton: ({ children, onClick, disabled }: any) => (
        <button data-testid="small-select-button" onClick={onClick} disabled={disabled}>
            {children}
        </button>
    ),
    SmallSelectDiv: ({ children }: any) => <div>{children}</div>
}));

jest.mock("@src/styles/forms", () => ({
    SubheadingText: ({ children }: any) => <h3>{children}</h3>
}));

jest.mock("../../common/DeviceDropdown", () => {
    return function DeviceDropdown({ id, value, setValue, devices, disabled }: any) {
        return (
            <select
                data-testid={`device-dropdown-${id}`}
                value={value || ''}
                onChange={(e) => setValue(e.target.value === '' ? undefined : e.target.value)}
                disabled={disabled}
            >
                <option value="">Select device</option>
                {devices.map((device: any) => (
                    <option key={device.deviceId} value={device.deviceId}>
                        {device.label || `Device ${device.deviceId}`}
                    </option>
                ))}
            </select>
        );
    };
});

// Create mock MediaStream and MediaStreamTrack
const createMockMediaStream = () => {
    const mockTracks = [
        {
            stop: jest.fn(),
            kind: 'video',
            id: 'video-track-1'
        },
        {
            stop: jest.fn(), 
            kind: 'audio',
            id: 'audio-track-1'
        }
    ];

    return {
        getTracks: jest.fn(() => mockTracks),
        getVideoTracks: jest.fn(() => mockTracks.filter(t => t.kind === 'video')),
        getAudioTracks: jest.fn(() => mockTracks.filter(t => t.kind === 'audio')),
        id: 'mock-stream-id'
    };
};

// Mock navigator.mediaDevices
const mockGetUserMedia = jest.fn();
const mockGetDisplayMedia = jest.fn();
const mockEnumerateDevices = jest.fn();

Object.defineProperty(global.navigator, 'mediaDevices', {
    value: {
        getUserMedia: mockGetUserMedia,
        getDisplayMedia: mockGetDisplayMedia,
        enumerateDevices: mockEnumerateDevices
    },
    writable: true
});

// Mock devices
const mockVideoDevices = [
    { deviceId: 'video1', kind: 'videoinput', label: 'Camera 1' },
    { deviceId: 'video2', kind: 'videoinput', label: 'Camera 2' }
];

const mockAudioDevices = [
    { deviceId: 'audio1', kind: 'audioinput', label: 'Microphone 1' },
    { deviceId: 'audio2', kind: 'audioinput', label: 'Microphone 2' }
];

// Import the component after all mocks are set up
const InputDevicesSelect = require("../InputDevicesSelect").default;

const renderComponent = (props = {}) => {
    const defaultProps = {
        disabled: false,
        videoStream: undefined,
        setVideoStream: jest.fn(),
        audioStream: undefined,
        setAudioStream: jest.fn(),
        screenStream: undefined,
        setScreenStream: jest.fn(),
        ...props
    };

    return render(
        <ThemeProvider theme={getTheme(null)}>
            <RecoilRoot>
                <InputDevicesSelect {...defaultProps} />
            </RecoilRoot>
        </ThemeProvider>
    );
};

describe("InputDevicesSelect", () => {
    beforeEach(() => {
        jest.clearAllMocks();
        
        // Setup default mock implementations
        mockGetUserMedia.mockResolvedValue(createMockMediaStream());
        mockGetDisplayMedia.mockResolvedValue(createMockMediaStream());
        mockEnumerateDevices.mockResolvedValue([...mockVideoDevices, ...mockAudioDevices]);
    });

    afterEach(() => {
        cleanup();
    });

    it("asks permission for video and microphone upon first load", async () => {
        renderComponent();

        await waitFor(() => {
            expect(mockGetUserMedia).toHaveBeenCalledWith({ video: true });
            expect(mockGetUserMedia).toHaveBeenCalledWith({ audio: true });
            expect(mockEnumerateDevices).toHaveBeenCalledTimes(2);
        });
    });

    it("changes video stream when a different video device is selected", async () => {
        const setVideoStream = jest.fn();
        const { getByTestId } = renderComponent({ setVideoStream });

        await waitFor(() => {
            expect(getByTestId('device-dropdown-camera-select')).toBeInTheDocument();
        });

        await act(async () => {
            fireEvent.change(getByTestId('device-dropdown-camera-select'), {
                target: { value: 'video2' }
            });
        });

        await waitFor(() => {
            expect(mockGetUserMedia).toHaveBeenCalledWith({
                video: { deviceId: 'video2' }
            });
            expect(setVideoStream).toHaveBeenCalled();
        });
    });

    it("changes audio stream when a different audio device is selected", async () => {
        const setAudioStream = jest.fn();
        const { getByTestId } = renderComponent({ setAudioStream });

        await waitFor(() => {
            expect(getByTestId('device-dropdown-mic-select')).toBeInTheDocument();
        });

        await act(async () => {
            fireEvent.change(getByTestId('device-dropdown-mic-select'), {
                target: { value: 'audio2' }
            });
        });

        await waitFor(() => {
            expect(mockGetUserMedia).toHaveBeenCalledWith({
                audio: { deviceId: 'audio2' }
            });
            expect(setAudioStream).toHaveBeenCalled();
        });
    });

    it("asks permission for screen share when select button is clicked", async () => {
        const setScreenStream = jest.fn();
        const { getByTestId } = renderComponent({ setScreenStream });

        await waitFor(() => {
            expect(getByTestId('small-select-button')).toBeInTheDocument();
        });

        await act(async () => {
            fireEvent.click(getByTestId('small-select-button'));
        });

        await waitFor(() => {
            expect(mockGetDisplayMedia).toHaveBeenCalledWith({ video: true });
            expect(setScreenStream).toHaveBeenCalled();
        });
    });

    it("frees active screenStream when 'stop sharing' button is clicked", async () => {
        const mockScreenStream = createMockMediaStream();
        const setScreenStream = jest.fn();
        const { getByTestId } = renderComponent({ 
            screenStream: mockScreenStream,
            setScreenStream 
        });

        await waitFor(() => {
            expect(getByTestId('small-select-button')).toBeInTheDocument();
        });

        await act(async () => {
            fireEvent.click(getByTestId('small-select-button'));
        });

        await waitFor(() => {
            expect(mockScreenStream.getTracks()[0].stop).toHaveBeenCalled();
            expect(setScreenStream).toHaveBeenCalledWith(undefined);
        });
    });

    it("frees all active streams on dismount", async () => {
        const mockVideoStream = createMockMediaStream();
        const mockAudioStream = createMockMediaStream();
        const mockScreenStream = createMockMediaStream();

        const { unmount } = renderComponent({
            videoStream: mockVideoStream,
            audioStream: mockAudioStream,
            screenStream: mockScreenStream
        });

        await act(async () => {
            unmount();
        });

        // Check that all tracks were stopped
        expect(mockVideoStream.getTracks()[0].stop).toHaveBeenCalled();
        expect(mockAudioStream.getTracks()[0].stop).toHaveBeenCalled();
        expect(mockScreenStream.getTracks()[0].stop).toHaveBeenCalled();
    });

    it("nothing can be interacted with when disabled is true", async () => {
        const { getByTestId } = renderComponent({ disabled: true });

        await waitFor(() => {
            expect(getByTestId('device-dropdown-camera-select')).toBeDisabled();
            expect(getByTestId('device-dropdown-mic-select')).toBeDisabled();
            expect(getByTestId('small-select-button')).toBeDisabled();
        });

        // Should not request permissions when disabled
        expect(mockGetUserMedia).not.toHaveBeenCalled();
        expect(mockEnumerateDevices).not.toHaveBeenCalled();
    });
});
