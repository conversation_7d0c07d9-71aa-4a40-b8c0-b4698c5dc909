import React from "react";
import RecordVideoPage from "../RecordVideo";
import { ThemeProvider } from "styled-components";
import getTheme from "@src/config/theme";
import { render, cleanup, act } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { RecoilRoot } from "recoil";

afterEach(cleanup);

jest.mock("react-router-dom", () => ({
    useParams: () => ({
        id: "*********"
    }),
    useNavigate: () => jest.fn()
}));

it("renders RecordVideoPage without crashing", async () => {
    await act(async () => {
        render(
            <ThemeProvider theme={getTheme(null)}>
                <RecoilRoot>
                    <RecordVideoPage />
                </RecoilRoot>
            </ThemeProvider>
        );
    });
});

it("redirects to index if allowRecordVideos is false", async () => {
    const mockNavigate = jest.fn();
    jest.mock("react-router-dom", () => ({
        useParams: () => ({
            id: "*********"
        }),
        useNavigate: () => mockNavigate
    }));

	jest.mock("getAccountData", () => ({
		__esModule: true,
		default: jest.fn().mockResolvedValue({
			data: {
				subscription: {
					allowRecordVideo: false
				}
			}
		})
	}));

    await act(async () => {
        render(
            <ThemeProvider theme={getTheme(null)}>
                <RecoilRoot>
                    <RecordVideoPage />
                </RecoilRoot>
            </ThemeProvider>
        );
    });
    expect(mockNavigate).toHaveBeenCalledWith("/");
});