import React from "react";
import RecordVideoPage from "../RecordVideo";
import { ThemeProvider } from "styled-components";
import getTheme from "@src/config/theme";
import { render, cleanup, act } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { RecoilRoot } from "recoil";

afterEach(cleanup);

jest.mock("../../utils/getAccountData", () => ({
	__esModule: true,
	default: jest.fn()
}));

jest.mock("../../hooks/useTokenCheck", () => ({
	useTokenCheck: () => ({
		apiRetryHandler: jest.fn((callback) => callback())
	})
}));

const mockNavigate = jest.fn();

jest.mock("react-router-dom", () => ({
	useParams: () => ({
		id: "*********"
	}),
	useNavigate: () => mockNavigate
}));

beforeEach(() => {
	mockNavigate.mockClear();
});

it("renders RecordVideoPage without crashing", async () => {
	const getAccountData = require("../../utils/getAccountData").default;
	getAccountData.mockResolvedValue({
		data: {
			subscription: {
				allowRecordVideo: true
			}
		}
	});

	await act(async () => {
		render(
			<ThemeProvider theme={getTheme(null)}>
				<RecoilRoot>
					<RecordVideoPage />
				</RecoilRoot>
			</ThemeProvider>
		);
	});
});

it("redirects to index if allowRecordVideos is false", async () => {
	const getAccountData = require("../../utils/getAccountData").default;
	getAccountData.mockResolvedValue({
		data: {
			subscription: {
				allowRecordVideo: false
			}
		}
	});

	await act(async () => {
		render(
			<ThemeProvider theme={getTheme(null)}>
				<RecoilRoot>
					<RecordVideoPage />
				</RecoilRoot>
			</ThemeProvider>
		);
	});

	expect(mockNavigate).toHaveBeenCalledWith("/");
});
