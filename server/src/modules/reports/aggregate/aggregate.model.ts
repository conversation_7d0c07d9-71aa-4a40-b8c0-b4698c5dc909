import { MetricPhonePressDBModel } from "./../../metricPhonePress/metricPhonePressDB.model";
import { getSecrets } from "../../secrets/secrets.model";
import { JobContainerModel } from "../../job/container/job.container.model";
import {
	JobsStatus,
	JobsType,
	Job
} from "../../job/job.model";
import { JobService } from "../../job/job.service";
import { AccountDBModel } from "../../account/accountDB.model";
import { IAccount } from "../../account/account.interfaces";
import {
	ReportsAggregate,
	ReportsAggregatePost,
	ReportsAggregatePlayTimeLookup,
	ReportsAggregateLeadLookup,
	ReportsAggregateURLInteractionLookup,
	ReportsAggregateVideo,
	ReportsAggregateURLInteraction,
	ReportsAggregateEngagedSession
} from "./aggregate.types";
import { ReportsDailyAggregateDBModel } from "./aggregate.schema";
import { InteractiveVideoDBModel } from "../../interactiveVideo/interactiveVideoDB.model";
import { IShoppableVideo } from "../../interactiveVideo/interactiveVideo.interface";
import { MetricVideoPlayTimeDBModel } from "../../metricVideoPlayTime/metricVideoPlayTimeDB.model";
import { MetricEmailSubmitDBModel } from "../../metricEmailSubmit/metricEmailSubmitDB.model";
import { MetricVideoEngagementDBModel } from "../../metricVideoEngagement/metricVideoEngagement.db.model";
import { MetricVideoClickDBModel } from "../../metricVideoClick/metricVideoClickDB.model";
import { MetricUserEngagementDBModel } from "../../metricUserEnagement/metricUserEnagementDB.model";
import { WithId } from "mongodb";
import mongoose, {
	ClientSession,
	ObjectId
} from "mongoose";
import { AccountModel } from "../../account/account.model";
import { IMetricImpression } from "../../metricImpression/metricImpression.interfaces";
import { MetricImpressionModel } from "../../metricImpression/metricImpression.model";

export enum WeekDay {
	SUNDAY = "sunday",
	MONDAY = "monday",
	TUESDAY = "tuesday",
	WEDNESDAY = "wednesday",
	THURSDAY = "thursday",
	FRIDAY = "friday",
	SATURDAY = "saturday"
}

export class ReportsAggregateModel {
	private session: ClientSession | null;

	constructor(session: ClientSession | null) {
		this.session = session;
	}

	/**
	 * Creates a new aggregation job based on the provided payload.
	 * The job is created with a status of 'CREATED' and is scheduled for processing.
	 * The method is called by the API controller to initiate the aggregation process.
	 * @param payload - The payload containing the date and range for aggregation.
	 */
	public async createAggregationJob(payload: ReportsAggregatePost): Promise<Job> {
		// Normalize date to midnight UTC
		const date = new Date(payload.date);
		date.setUTCHours(0, 0, 0, 0);

		let type = JobsType.REPORTS_AGGREGATE;
		if (payload.since) {
			type = JobsType.REPORTS_AGGREGATE_SINCE;
		}

		const data: any = {
			type: type,
			status: JobsStatus.CREATED,
			statusMessage: "reports aggregate job has been created.",
			aggregateDate: date
		};

		if (payload.accountId) {
			data.accountId = payload.accountId;
		}

		const jobService = new JobService(this.session);
		const job = await jobService.createJob(data);

		const jobTimeoutSeconds = 3600 * 24;
		const secrets = await getSecrets();
		const jobContainerModel = new JobContainerModel(this.session, secrets.storage.isLocal);
		await jobContainerModel.runJobWorker(job._id.toString(), jobTimeoutSeconds);
		return job;
	}

	/**
	 * Aggregates accounts based on the provided job.
	 * This method retrieves all accounts and processes each one according to the specified aggregation range.
	 * This method is called by the job worker to perform the aggregation.
	 * @param job - The job containing the aggregation date and range.
	 */
	public async aggregateAccounts(job: Job): Promise<void> {
		if (!job.aggregateDate) {
			throw new Error("Job does not have an aggregateDate set.");
		}

		const jobService = new JobService(null);
		await jobService.updateJobStatus(job._id.toString(), JobsStatus.RUNNING, "Aggregation job is running...");

		const since = job.aggregateDate;
		const now = new Date();

		if (job.accountId) {
			const accountModel = new AccountModel(null);
			const account = await accountModel.readOneById(job.accountId.toString());
			if (!account) {
				throw new Error(`Account with id ${job.accountId} not found.`);
			}

			if (job.type === JobsType.REPORTS_AGGREGATE_SINCE) {
				await this.aggregateSingleAccountJob(account, since, now);
			} else {
				await this.aggregateSingleAccountJob(account, job.aggregateDate);
			}
		} else {
			if (job.type === JobsType.REPORTS_AGGREGATE_SINCE) {
				await this.aggregateAllAccountsJob(job, since, now);
			} else {
				await this.aggregateAllAccountsJob(job, job.aggregateDate);
			}
		}

		await jobService.updateJobStatus(
			job._id.toString(),
			JobsStatus.COMPLETE,
			"Aggregation completed successfully."
		);
	}

	private async aggregateSingleAccountJob(
		account: IAccount,
		dateOrSince: Date,
		endDate?: Date
	): Promise<void> {
		if (endDate) {
			await this.forEachDayInRange(dateOrSince, endDate, async (date) => {
				await this.aggregateAccount(account, date);
			});
		} else {
			await this.aggregateAccount(account, dateOrSince);
		}
	}

	private async aggregateAllAccountsJob(job: Job, dateOrSince: Date, endDate?: Date): Promise<void> {
		if (endDate) {
			await this.forEachDayInRange(dateOrSince, endDate, async (date) => {
				await this.aggregateAllAccountsForDay(date);
			});
		} else {
			await this.aggregateAllAccountsForDay(dateOrSince);
		}
	}

	private async aggregateAllAccountsForDay(date: Date): Promise<void> {
		const accountsDBModel = new AccountDBModel(this.session);
		const accountsCursor = accountsDBModel.collection.find<IAccount>({});
		for await (const account of accountsCursor) {
			await this.aggregateAccount(account, date);
		}
	}

	private async aggregatePlayTime(
		accountId: ObjectId,
		startDate: Date,
		endDate: Date
	): Promise<ReportsAggregatePlayTimeLookup[]> {
		const metricVideoPlayTimeModel = new MetricVideoPlayTimeDBModel(this.session);
		const result = await metricVideoPlayTimeModel.collection.aggregate([
			{
				$match: {
					accountId: accountId,
					videoPlayStatus: "stopped",
					createdAt: { $gte: startDate, $lte: endDate }
				}
			},
			{
				$group: {
					_id: "$videoId",
					totalPlayTime: { $sum: "$totalPlayTimeSeconds" },
					totalPlays: { $sum: 1 }
				}
			}
		]).toArray();
		return result as ReportsAggregatePlayTimeLookup[];
	}

	private async aggregatePhoneLeads(
		accountId: ObjectId,
		startDate: Date,
		endDate: Date
	): Promise<ReportsAggregateLeadLookup[]> {
		const metricPhonePressDBModel = new MetricPhonePressDBModel(this.session);
		const result = await metricPhonePressDBModel.collection.aggregate([
			{
				$match: {
					accountId: accountId,
					createdAt: { $gte: startDate, $lte: endDate }
				}
			},
			{
				$group: { _id: "$videoId", totalLeads: { $sum: 1 } }
			}
		]).toArray();
		return result as ReportsAggregateLeadLookup[];
	}

	private async aggregateEmailLeads(
		accountId: ObjectId,
		startDate: Date,
		endDate: Date
	): Promise<ReportsAggregateLeadLookup[]> {
		const metricEmailSubmitDBModel = new MetricEmailSubmitDBModel(this.session);
		const result = await metricEmailSubmitDBModel.collection.aggregate([
			{
				$match: {
					accountId: accountId,
					createdAt: { $gte: startDate, $lte: endDate }
				}
			},
			{
				$group: { _id: "$videoId", totalLeads: { $sum: 1 } }
			}
		]).toArray();
		return result as ReportsAggregateLeadLookup[];
	}

	private async aggregateEngagement(
		video: IShoppableVideo,
		startDate: Date,
		endDate: Date
	): Promise<number> {
		const metricVideoEngagementDBModel = new MetricVideoEngagementDBModel(this.session);
		const result = await metricVideoEngagementDBModel.collection.aggregate([
			{
				$match: {
					videoId: video._id,
					createdAt: { $gte: startDate, $lte: endDate }
				}
			},
			{
				$group: { _id: null, avgEngagementScore: { $avg: "$videoScore" } }
			}
		]).toArray();
		return result[0]?.avgEngagementScore !== undefined
			? Math.round(result[0].avgEngagementScore ?? 0)
			: 0;
	}

	private async aggregateUrlInteractions(
		accountId: ObjectId,
		startDate: Date,
		endDate: Date
	): Promise<ReportsAggregateURLInteractionLookup[]> {
		const metricVideoClickDBModel = new MetricVideoClickDBModel(this.session);
		const urlInteractionsResult = await metricVideoClickDBModel.collection.aggregate([
			{
				$match: {
					accountId: accountId,
					createdAt: { $gte: startDate, $lte: endDate }
				}
			},
			{
				$group: {
					_id: {
						videoId: "$videoId",
						productId: "$productId"
					}, totalClicks: { $sum: 1 }
				}
			}
		]).toArray();

		return urlInteractionsResult as ReportsAggregateURLInteractionLookup[];
	}

	private async aggregateImpressions(
		account: IAccount,
		startDate: Date,
		endDate: Date
	): Promise<number> {
		const matchQuery: mongoose.FilterQuery<IMetricImpression> = {
			accountId: account._id,
			createdAt: {
				$gte: startDate,
				$lte: endDate
			}
		};

		const metricImpressionModel = new MetricImpressionModel(null);
		const totalImpressions = await metricImpressionModel.countDocuments(matchQuery);
		return totalImpressions;
	}

	private async aggregateUserEngagedSessions(
		account: IAccount,
		startDate: Date,
		endDate: Date
	): Promise<ReportsAggregateEngagedSession> {
		const metricUserEngagementDBModel = new MetricUserEngagementDBModel(this.session);

		const result = await metricUserEngagementDBModel.collection.aggregate([
			{
				$match: {
					accountId: account._id,
					createdAt: { $gte: startDate, $lte: endDate }
				}
			},
			{
				$group: {
					_id: "$userSessionId"
				}
			},
			{
				$group: {
					_id: null,
					sessionIds: { $addToSet: "$_id" },
					totalSessions: { $sum: 1 }
				}
			},
			{
				$project: {
					_id: 0,
					sessionIds: 1,
					totalSessions: 1
				}
			}
		]).toArray();

		return (result[0] ?? { sessionIds: [], totalSessions: 0 }) as ReportsAggregateEngagedSession;
	}

	private captureDateToStartEndDates(captureDate: Date): { startDate: Date; endDate: Date } {
		const startDate = new Date(Date.UTC(
			captureDate.getFullYear(),
			captureDate.getMonth(),
			captureDate.getDate(),
			0,
			0,
			0,
			0
		));

		const endDate = new Date(Date.UTC(
			captureDate.getFullYear(),
			captureDate.getMonth(),
			captureDate.getDate(),
			23,
			59,
			59,
			999
		));

		return { startDate, endDate };
	}

	private async aggregateAccount(
		account: IAccount,
		captureDate: Date
	): Promise<void> {
		const { startDate, endDate } = this.captureDateToStartEndDates(captureDate);

		const reportsAggregateVideos: ReportsAggregateVideo[] = [];
		const videoDBModel = new InteractiveVideoDBModel(this.session);
		const videoCursor = videoDBModel.collection.find<IShoppableVideo>({ accountId: account._id });

		const playTimes = await this.aggregatePlayTime(account._id, startDate, endDate);

		const playTimesLookup = new Map(playTimes.map(item =>
			[item._id.toString(), { totalPlays: item.totalPlays, totalPlayTime: item.totalPlayTime }]
		));

		const totalPlays = playTimes.reduce((sum, i) => sum + i.totalPlays, 0);
		const totalPlayTime = playTimes.reduce((sum, i) => sum + i.totalPlayTime, 0);

		const phoneLeads = await this.aggregatePhoneLeads(account._id, startDate, endDate);

		const emailLeads = await this.aggregateEmailLeads(account._id, startDate, endDate);
		const clicks = await this.aggregateUrlInteractions(account._id, startDate, endDate);

		const phoneLeadsLookup = new Map(phoneLeads.map(item => [item._id.toString(), item.totalLeads]));
		const emailLeadsLookup = new Map(emailLeads.map(item => [item._id.toString(), item.totalLeads]));
		const clicksLookup = clicks.reduce(
			(map, { _id, totalClicks }) => {
				const { videoId, productId } = _id;
				const key = videoId.toString();
				const prodId = productId.toString();

				if (!map.has(key)) {
					map.set(key, {
						records: [],
						totalClicks: 0
					});
				}

				const entry = map.get(key);
				if (entry) {
					entry.records.push({ productId: prodId, totalClicks });
					entry.totalClicks += totalClicks;
				}

				return map;
			},
			new Map<
				string,
				{
					records: Array<{ productId: string; totalClicks: number; }>;
					totalClicks: number;
				}
			>()
		);

		for await (const video of videoCursor) {
			const engagement = await this.aggregateEngagement(video, startDate, endDate);
			const clicksLookupResult = clicksLookup.get(video._id.toString());

			const urlInteractions = clicksLookupResult?.records.map(interaction => {
				const product = video.linkClicks?.find(p =>
					p.productId.toString() === interaction.productId?.toString()
				);
				if (product?.productURL) {
					return {
						url: product.productURL,
						count: interaction.totalClicks
					} as ReportsAggregateURLInteraction;
				}
				return null;
			}).filter((i): i is ReportsAggregateURLInteraction => i !== null) as ReportsAggregateURLInteraction[];

			const videoAggregate: ReportsAggregateVideo = {
				interactiveVideoTitle: video.title,
				gifURL: video.gifURL,
				posterURL: video.videoPosterURL,
				interactiveVideoId: video._id,
				videoId: video.videoId,
				plays: playTimesLookup.get(video._id.toString())?.totalPlays ?? 0,
				clicks: clicksLookup.get(video._id.toString())?.totalClicks ?? 0,
				emailLeads: emailLeadsLookup.get(video._id.toString()) ?? 0,
				callLeads: phoneLeadsLookup.get(video._id.toString()) ?? 0,
				playTimeSeconds: playTimesLookup.get(video._id.toString())?.totalPlayTime ?? 0,
				engagementScore: engagement,
				likes: 0,
				urlInteractions: urlInteractions,
				videoLengthSeconds: video.videoTotalSeconds || 0
			};

			reportsAggregateVideos.push(videoAggregate);
		}

		const totalEmails = emailLeads.reduce((sum, i) => sum + i.totalLeads, 0);
		const totalCalls = phoneLeads.reduce((sum, i) => sum + i.totalLeads, 0);
		const totalClicks = clicks.reduce((sum, i) => sum + i.totalClicks, 0);
		const totalImpressions = await this.aggregateImpressions(account, startDate, endDate);
		const allEngagedSessions = await this.aggregateUserEngagedSessions(account, startDate, endDate);
		const reportsAggregate: ReportsAggregate = {
			accountId: account._id,
			createdAt: new Date(),
			updatedAt: new Date(),
			captureDate: captureDate,
			videos: reportsAggregateVideos,
			totalPlays: totalPlays,
			totalPlayTime: totalPlayTime,
			totalEmails: totalEmails,
			totalCalls: totalCalls,
			totalClicks: totalClicks,
			userSessionIds: allEngagedSessions.sessionIds,
			totalEngagedSessions: allEngagedSessions.totalSessions,
			totalImpressions: totalImpressions
		};

		await this.writeAggregatedToDB(account, captureDate, reportsAggregate);
	}

	private async writeAggregatedToDB(
		account: IAccount,
		date: Date,
		reportsAggregate: ReportsAggregate
	): Promise<void> {
		const dbModel = new ReportsDailyAggregateDBModel(this.session);
		const captureDate = date;

		const existingRecord = await dbModel.collection.findOne<WithId<ReportsAggregate>>({
			accountId: account._id,
			captureDate: captureDate
		});

		if (existingRecord) {
			await dbModel.collection.updateOne(
				{ _id: existingRecord._id },
				{
					$set: {
						accountId: account._id,
						videos: reportsAggregate.videos,
						totalPlays: reportsAggregate.totalPlays,
						totalPlayTime: reportsAggregate.totalPlayTime,
						totalEmails: reportsAggregate.totalEmails,
						totalCalls: reportsAggregate.totalCalls,
						totalClicks: reportsAggregate.totalClicks,
						userSessionIds: reportsAggregate.userSessionIds,
						totalEngagedSessions: reportsAggregate.totalEngagedSessions,
						totalImpressions: reportsAggregate.totalImpressions,
						updatedAt: new Date(),
						captureDate: captureDate
					}
				}
			);
		} else {
			await dbModel.collection.insertOne(reportsAggregate);
		}
	}

	public async readAggregatedRangeFromDB(
		account: IAccount,
		startDate: Date,
		endDate: Date
	): Promise<ReportsAggregate[]> {
		const dbModel = new ReportsDailyAggregateDBModel(this.session);
		return await dbModel.collection.find<ReportsAggregate>({
			accountId: account._id,
			captureDate: { $gte: startDate, $lt: endDate }
		}).sort({ captureDate: 1 }).toArray();
	}

	private async forEachDayInRange(
		start: Date,
		end: Date,
		fn: (date: Date) => Promise<void>
	): Promise<void> {
		const promises: Promise<void>[] = [];
		const current = new Date(start);
		while (current.getTime() <= end.getTime()) {
			promises.push(fn(new Date(current)));
			current.setDate(current.getDate() + 1);
		}
		await Promise.all(promises);
	}
}
