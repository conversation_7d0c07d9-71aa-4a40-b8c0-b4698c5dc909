import Jo<PERSON> from "joi";
import { BaseJoi } from "../base/base.joi";
import { APIErrorName } from "../../interfaces/apiTypes";
import mongoose, {
	Document,
	ObjectId,
	Schema
} from "mongoose";
import { ReportRange } from "../reports/generate/reports.types";
import { AvatarPost } from "../avatar/avatar.model";
import { BaseRequest } from "../base/base.interfaces";

export enum JobsType {
	ENCODE_VIDEO = "encodeVideo",
	OPTIMIZE_IMAGE = "optimizeImage",
	SYNC_ACCOUNTS = "syncAccounts",
	REPORTS_AGGREGATE = "reportsAggregate",
	REPORTS_AGGREGATE_SINCE = "reportsAggregateSince",
	REPORTS_GENERATE = "reportsGenerate",
	AVATAR_VIDEO = "avatarVideo",
}

export enum JobsStatus {
	CREATED = "created",
	RUNNING = "running",
	COMPLETE = "complete",
	FAILED = "failed"
}

export interface Job extends Document {
	_id: ObjectId;
	tempFilename: string;
	accountId?: ObjectId;
	userId?: ObjectId;
	type: JobsType;
	status: JobsStatus;
	statusMessage: string;
	createdAt: number;
	updatedAt: number;
	sourceURL?: string;
	progressPercent?: number;
	nextStatusCheck?: number;
	videoId?: ObjectId;
	imageWidthPx?: number;
	imageHeightPx?: number;
	imageURL?: string;
	callbackInfo?: CallbackInfo;
	reportRange?: ReportRange;
	reportDate?: Date;
	aggregateDate?: Date;
	avatarPost?: AvatarPost;
	redirectEmail?: string;
}

export interface CallbackInfo {
	callbackUrl?: string;
	callbackData?: string;
}

export interface IJobInput {
	imageWidthPx?: number;
	imageHeightPx?: number;
}

export interface IJobPayload {
	jobType: string;
	sourceURL: string;
	imageWidthPx: number;
	imageHeightPx: number;
}

export interface JobGet extends BaseRequest {
	id: string;
}

export interface JobList extends BaseRequest {
	type?: JobsType;
	status?: JobsStatus;
}


export const JobListSchema = BaseJoi.append<JobList>({
	type: Joi.string()
		.valid(...Object.values(JobsType))
		.optional(),
	status: Joi.string()
		.valid(...Object.values(JobsStatus))
		.optional()
});

export const JobGetSchema = BaseJoi.append<JobGet>({
	id: Joi.string()
		.required()
		.custom((value, helpers) => {
			if (!/^[a-fA-F0-9]{24}$/.test(value)) {
				return helpers.error("any.invalid");
			}
			return value;
		})
});

export const postJobSchema = {
	data: Joi.object<IJobPayload>({
		jobType: Joi.string()
			.valid(...Object.values(JobsType))
			.required(),
		sourceURL: Joi.string().min(1).required(),
		imageWidthPx: Joi.number().optional(),
		imageHeightPx: Joi.number().optional()
	}).messages({
		"any.required": APIErrorName.E_INVALID_INPUT,
		"string.empty": APIErrorName.E_INVALID_INPUT
	})
};

const JobSchema: Schema = new Schema({
	tempFilename: {
		type: String,
		unique: true,
		validate: {
			validator: function(this: any, v: string): boolean {
				if (this.type === JobsType.REPORTS_AGGREGATE) {
					return true;
				}

				return typeof v === "string" && v.trim().length > 0;
			},
			message: `tempFilename is required unless type is '${JobsType.REPORTS_AGGREGATE}'.`
		}
	},
	accountId: { type: Schema.Types.ObjectId, required: false },
	userId: { type: Schema.Types.ObjectId, required: false },
	type: {
		type: String,
		required: true,
		enum: Object.values(JobsType)
	},
	status: { type: String, required: true },
	statusMessage: { type: String, required: true },
	createdAt: { type: Number, default: () => Date.now() },
	updatedAt: { type: Number, default: () => Date.now() },
	sourceURL: { type: String, required: false },
	progressPercent: { type: Number, required: false },
	nextStatusCheck: { type: Number, required: false },
	videoId: { type: Schema.Types.ObjectId, required: false },
	imageWidthPx: { type: Schema.Types.Number, required: false },
	imageHeightPx: { type: Schema.Types.Number, required: false },
	imageURL: { type: Schema.Types.String, required: false },
	callbackInfo: { type: { callbackUrl: String, callbackData: String }, required: false },
	reportRange: {
		type: String,
		validate: {
			validator: function(this: any, v: string): boolean {
				if (this.type === JobsType.REPORTS_GENERATE) {
					if (typeof v !== "string" || v.trim().length === 0) return false;
					const normalized = v.toUpperCase() as ReportRange;
					return Object.values(ReportRange).includes(normalized);
				}
				return true;
			},
			message: "reportRange is required and must be a valid ReportRange"
		},
		required: function(this: any): boolean {
			return this.type === JobsType.REPORTS_GENERATE;
		}
	},
	reportDate: {
		type: Date,
		validate: {
			validator: function(this: any, v: Date): boolean {
				if (this.type === JobsType.REPORTS_GENERATE) {
					return v instanceof Date && !isNaN(v.getTime());
				}
				return true;
			},
			message: "reportDate is required and must be a valid Date"
		},
		required: function(this: any): boolean {
			return this.type === JobsType.REPORTS_GENERATE;
		}
	},
	redirectEmail: {
		type: Schema.Types.String,
		required: false
	},
	aggregateDate: {
		type: Date,
		validate: {
			validator: function(this: any, v: Date): boolean {
				if (this.type === JobsType.REPORTS_AGGREGATE) {
					return v instanceof Date && !isNaN(v.getTime());
				}
				return true;
			},
			message: "aggregateDate is required and must be a valid Date"
		},
		required: function(this: any): boolean {
			return this.type === JobsType.REPORTS_AGGREGATE;
		}
	},
	avatarPost: {
		type: {
			lookId: { type: String, required: true },
			voiceId: { type: String, required: true },
			text: { type: String, required: true },
			callbackInfo: {
				type: {
					callbackUrl: { type: String, required: false },
					callbackData: { type: String, required: false }
				},
				required: false
			}
		},
		required: function(this: any): boolean {
			return this.type === JobsType.AVATAR_VIDEO;
		}
	}
}, {
	timestamps: true
});

export const JobModel = mongoose.model<Job>(
	"Jobs",
	JobSchema
);
