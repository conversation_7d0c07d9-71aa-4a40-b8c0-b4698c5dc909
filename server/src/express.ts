import express, {
	Express,
	Request,
	Response,
	NextFunction
} from "express";
import cors from "cors";
import { IAccessToken } from "./modules/accessToken/accessToken.interface";
import {
	FilesRoute,
	InvitationsRoute,
	RootRoute,
	ShoppableCollectionsRoute,
	UsersRoute,
	MetricsRoute
} from "./routes";
import ShoppableVideosRoute from "./routes/shoppableVideos.route";
import AuthRoute from "./routes/auth.route";
import { allRoutes } from "./middleware/allRoutes.mw";
import { IAccountToken } from "./modules/account/account.interfaces";
import { AccountController } from "./modules/account/account.controller";
import { AccountSuperRouter } from "./modules/account/super/account.super.router";
import { AccountSelfRouter } from "./modules/account/self/account.self.router";
import { AccountTokenRouter } from "./modules/account/token/account.token.router";
import { AccountUsersRouter } from "./modules/account/user/account.user.router";
import { AccountSyncRouter } from "./modules/account/sync/account.sync.router";
import { JobCleanRouter } from "./modules/job/clean/job.clean.router";
import { AdminRouter } from "./modules/admin/admin.router";
import { AuthLinkRouter } from "./modules/authentication/link/authlink.router";
import { APIKeyRouter } from "./modules/apiKey/apiKey.router";
import { StripeWebhookController } from "./modules/stripe/webhook/webhook.controller";
import { ProductController } from "./modules/product/product.controller";
import { SubscriptionController } from "./modules/subscription/subscription.controller";
import { PaymentController } from "./modules/payment/payment.controller";
import {
	gpLog,
	LogScope
} from "./utils/managers/gpLog.manager";
import { OIDCController } from "./modules/oidc/oidc.controller";
import { OIDCTokenController } from "./modules/oidc/token/oidc.token.controller";
import { EmailController } from "./modules/email/email.controller";
import { VideoSignedURLController } from "./modules/video/signedURL/video.signedURL.controller";
import { VideoEventUploadedController } from "./modules/video/event/video.event.uploaded.controller";
import { VideoURLController } from "./modules/video/url/video.url.controller";
import { VideoJobStatusController } from "./modules/video/job/video.job.status.controller";
import { VideoController } from "./modules/video/video.controller";
import { VideoJobController } from "./modules/video/job/video.job.controller";
import { EventBufferController } from "./modules/eventBuffer/eventBuffer.controller";
import { VideoProfileController } from "./modules/videoProfile/videoProfile.controller";
import { PlayTimeProcessorController } from "./modules/metricVideoPlayTime/processor/playTime.processor.controller";
import { SignupEnterpriseController } from "./modules/signupEnterprise/signupEnterprise.controller";
import { ReportsAggregateController } from "./modules/reports/aggregate/aggregate.controller";
import { ReportsGenerateController } from "./modules/reports/generate/reports.controller";
import { JobController } from "./modules/job/job.controller";
import { AvatarController } from "./modules/avatar/avatar.controller";
import { LookController } from "./modules/avatar/look/look.controller";
import { VoiceController } from "./modules/avatar/voice/voice.controller";

declare global {
	// eslint-disable-next-line @typescript-eslint/no-namespace
	namespace Express {
		export interface Request {
			accessToken: IAccessToken | undefined;
			accountToken: IAccountToken | undefined;
			superWritePermission: boolean | undefined;
		}
	}
}

export const createServer = (): Express => {
	const app: Express = express();
	app.use(express.urlencoded({ extended: true }));
	app.all("*", allRoutes);
	app.use(cors());
	app.use((req: Request, res: Response, next: NextFunction) => {

		res.header("Access-Control-Allow-Origin", "*");
		res.header(
			"Access-Control-Allow-Headers",
			"Origin, X-Requested-With, Content-Type, Accept, Authorization"
		);
		next();
	});
	return app;
};

export const initExpressServer = async (port: number): Promise<Express> => {
	return new Promise((resolve, reject) => {
		const expressServer = createServer();

		expressServer.listen({ port: port }, () => {
			gpLog({
				message: `⚡️ Express server listening on port ${port}`,
				trace: "express",
				scope: LogScope.INFO
			});

			expressServer ? resolve(expressServer) : reject(expressServer);
		});
	});
};

/**
 * Initializes the http routes that Express will serve.
 * @param {Express} expressServer an initialized Express server instance.
 */
export const initExpressRoutes = (expressServer: Express): void => {
	expressServer.use("/api", RootRoute);

	const accountSelfRouter: AccountSelfRouter = new AccountSelfRouter();
	accountSelfRouter.use(expressServer);

	const accountSuperRouter: AccountSuperRouter = new AccountSuperRouter();
	accountSuperRouter.use(expressServer);

	const accountTokenRouter: AccountTokenRouter = new AccountTokenRouter();
	accountTokenRouter.use(expressServer);

	const accountUserRouter: AccountUsersRouter = new AccountUsersRouter();
	accountUserRouter.use(expressServer);

	const accountController: AccountController = new AccountController();
	accountController.use(expressServer, "/api/accounts");

	const reportsAggregateController: ReportsAggregateController = new ReportsAggregateController();
	reportsAggregateController.use(expressServer, "/api/reports/aggregate");

	const reportsGenerateController: ReportsGenerateController = new ReportsGenerateController();
	reportsGenerateController.use(expressServer, "/api/reports/generate");

	expressServer.use("/api/auth", AuthRoute);

	const authLinkRouter: AuthLinkRouter = new AuthLinkRouter();
	authLinkRouter.use(expressServer);

	expressServer.use("/api/files", FilesRoute);

	expressServer.use("/api/shoppable-collections", ShoppableCollectionsRoute);

	expressServer.use("/api/shoppable-videos", ShoppableVideosRoute);

	expressServer.use("/api/users", UsersRoute);

	expressServer.use("/api/invitations", InvitationsRoute);

	expressServer.use("/api/metrics", MetricsRoute);

	const apiKeyRouter: APIKeyRouter = new APIKeyRouter();
	apiKeyRouter.use(expressServer);

	const accountSyncRouter: AccountSyncRouter = new AccountSyncRouter();
	accountSyncRouter.use(expressServer);

	const jobController: JobController = new JobController();
	jobController.use(expressServer, "/api/jobs");

	const jobCleanRouter: JobCleanRouter = new JobCleanRouter();
	jobCleanRouter.use(expressServer);

	const webhookController = new StripeWebhookController();
	webhookController.use(expressServer, "/api/stripe/webhook");

	const paymentController = new PaymentController();
	paymentController.use(expressServer, "/api/stripe/payment");

	const productController = new ProductController();
	productController.use(expressServer, "/api/products");

	const subscriptionController = new SubscriptionController();
	subscriptionController.use(expressServer, "/api/subscriptions");

	const oidcController = new OIDCController();
	oidcController.use(expressServer, "/api/oauth/oidc");

	const oidcTokenController = new OIDCTokenController();
	oidcTokenController.use(expressServer, "/api/oauth/oidc/token");

	const emailController = new EmailController();
	emailController.use(expressServer, "/api/email");

	const videoURLController = new VideoURLController();
	videoURLController.use(expressServer, "/api/videos/url");

	const videoJobController = new VideoJobController();
	videoJobController.use(expressServer, "/api/videos/job");

	const videoJobStatusController = new VideoJobStatusController();
	videoJobStatusController.use(expressServer, "/api/videos/job/status");

	const videoSignedUrlController = new VideoSignedURLController();
	videoSignedUrlController.use(expressServer, "/api/videos/signed-url");

	const videoEventUploadedController = new VideoEventUploadedController();
	videoEventUploadedController.use(expressServer, "/api/videos/events/uploaded");

	const eventBufferController = new EventBufferController();
	eventBufferController.use(expressServer, "/api/event/buffer");

	const playTimeProcessorController = new PlayTimeProcessorController();
	playTimeProcessorController.use(expressServer, "/api/playtime/processor");

	const videoController = new VideoController();
	videoController.use(expressServer, "/api/videos");

	const videoProfileController = new VideoProfileController();
	videoProfileController.use(expressServer, "/api/video-profiles");

	const signupEnterpriseController = new SignupEnterpriseController();
	signupEnterpriseController.use(expressServer, "/api/auth/signup/enterprise");

	const avatarController: AvatarController = new AvatarController();
	avatarController.use(expressServer, "/api/avatar");

	const avatarLookController: LookController = new LookController();
	avatarLookController.use(expressServer, "/api/avatar/look");

	const avatarVoiceController: VoiceController = new VoiceController();
	avatarVoiceController.use(expressServer, "/api/avatar/voice");

	expressServer.get("/api*", (request: Request, response: Response) => {
		const error = new Error("Request Not Found.");
		return response.status(404).json({
			message: error.message
		});
	});

	const adminRouter: AdminRouter = new AdminRouter();
	adminRouter.use(expressServer);

	// Error handling
	expressServer.use((req: Request, res: Response) => {
		const error = new Error("Request Not Found.");
		return res.status(404).json({
			message: error.message
		});
	});

	gpLog({
		message: "Express routes applied",
		trace: "express",
		scope: LogScope.INFO
	});
};
